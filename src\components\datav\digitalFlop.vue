<template>
  <div id="digital-flop">
    <div
      class="digital-flop-item"
      v-for="item in digitalFlopData"
      :key="item.title"
    >
      <div class="card-header">
        <div class="card-icon">
          <i :class="item.icon"></i>
        </div>
        <div class="card-title">{{ item.title }}</div>
      </div>

      <div class="card-content">
        <div class="main-data">
          <div class="main-number">
            <dv-digital-flop
              :config="item.mainNumber"
              style="width:150px;height:80px;"
            />
            <span class="main-unit">{{ item.mainUnit }}</span>
          </div>
        </div>

        <div class="sub-data" v-if="item.subData">
          <div class="sub-item" v-for="sub in item.subData" :key="sub.label">
            <div class="sub-label">{{ sub.label }}</div>
            <div class="sub-value">
              <dv-digital-flop
                :config="sub.number"
                style="width:80px;height:40px;"
              />
              <span class="sub-unit">{{ sub.unit }}</span>
            </div>
          </div>
        </div>

        <div class="additional-info" v-if="item.additionalInfo">
          <div class="info-item" v-for="info in item.additionalInfo" :key="info.label">
            <span class="info-label">{{ info.label }}</span>
            <span class="info-value">{{ info.value }}</span>
          </div>
        </div>

        <div class="progress-bar" v-if="item.progress">
          <div class="progress-fill" :style="{ width: item.progress + '%' }"></div>
          <div class="progress-text">{{ item.progressLabel }}: {{ item.progress }}%</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DigitalFlop',
  data () {
    return {
      digitalFlopData: []
    }
  },
  methods: {
    createData () {
      this.digitalFlopData = [
        {
          title: '教师情况',
          icon: 'fas fa-user-tie',
          mainNumber: {
            number: [715],
            content: '{nt}',
            textAlign: 'center',
            style: {
              fill: '#58a1ff',
              fontWeight: 'bold',
              fontSize: '48px'
            }
          },
          mainUnit: '人',
          subData: [
            {
              label: '请假',
              number: {
                number: [10],
                content: '{nt}',
                textAlign: 'center',
                style: {
                  fill: '#26fcd8',
                  fontWeight: 'bold',
                  fontSize: '20px'
                }
              },
              unit: '人'
            },
            {
              label: '公差',
              number: {
                number: [11],
                content: '{nt}',
                textAlign: 'center',
                style: {
                  fill: '#26fcd8',
                  fontWeight: 'bold',
                  fontSize: '20px'
                }
              },
              unit: '人'
            }
          ],
          progress: 96,
          progressLabel: '在校率'
        },
        {
          title: '学生情况',
          icon: 'fas fa-user-graduate',
          mainNumber: {
            number: [4450],
            content: '{nt}',
            textAlign: 'center',
            style: {
              fill: '#58a1ff',
              fontWeight: 'bold',
              fontSize: '32px'
            }
          },
          mainUnit: '',
          subData: [
            {
              label: '应在校学生',
              number: {
                number: [4450],
                content: '{nt}',
                textAlign: 'center',
                style: {
                  fill: '#26fcd8',
                  fontWeight: 'bold',
                  fontSize: '20px'
                }
              },
              unit: ''
            },
            {
              label: '实际在校',
              number: {
                number: [4285],
                content: '{nt}',
                textAlign: 'center',
                style: {
                  fill: '#26fcd8',
                  fontWeight: 'bold',
                  fontSize: '20px'
                }
              },
              unit: ''
            }
          ],
          additionalInfo: [
            { label: '学生出校人数', value: '165人' },
            { label: '校外人数', value: '28人' }
          ],
          progress: 96.3,
          progressLabel: '在校率'
        },
        {
          title: '今日进出门次',
          icon: 'fas fa-door-open',
          mainNumber: {
            number: [1245],
            content: '{nt}',
            textAlign: 'center',
            style: {
              fill: '#58a1ff',
              fontWeight: 'bold',
              fontSize: '32px'
            }
          },
          mainUnit: '',
          subData: [
            {
              label: '进门次',
              number: {
                number: [1245],
                content: '{nt}',
                textAlign: 'center',
                style: {
                  fill: '#26fcd8',
                  fontWeight: 'bold',
                  fontSize: '20px'
                }
              },
              unit: ''
            },
            {
              label: '出门次',
              number: {
                number: [1180],
                content: '{nt}',
                textAlign: 'center',
                style: {
                  fill: '#26fcd8',
                  fontWeight: 'bold',
                  fontSize: '20px'
                }
              },
              unit: ''
            }
          ],
          additionalInfo: [
            { label: '高峰时段', value: '16:00-17:00' },
            { label: '当前在校', value: '4,285人' }
          ]
        },
        {
          title: '通知公告',
          icon: 'fas fa-bell',
          mainNumber: {
            number: [3],
            content: '{nt}',
            textAlign: 'center',
            style: {
              fill: '#58a1ff',
              fontWeight: 'bold',
              fontSize: '48px'
            }
          },
          mainUnit: '条',
          additionalInfo: [
            { label: '欢迎新学期！', value: '2024-08-12' },
            { label: '门禁系统升级通知', value: '2024-08-10' },
            { label: '安全管理规定', value: '2024-08-08' }
          ]
        }
      ]
    }
  },
  mounted () {
    const { createData } = this

    createData()

    setInterval(createData, 30000)
  }
}
</script>

<style lang="less">
#digital-flop {
  position: relative;
  height: 20%;
  min-height: 200px;
  flex-shrink: 0;
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  gap: 20px;
  padding: 15px;
  background: transparent;

  .digital-flop-item {
    flex: 1;
    background: linear-gradient(135deg, rgba(6, 30, 93, 0.8) 0%, rgba(6, 30, 93, 0.6) 100%);
    border: 1px solid rgba(1, 153, 209, 0.3);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #03d3ec 0%, #26fcd8 50%, #58a1ff 100%);
    }
  }

  .card-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    .card-icon {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #03d3ec 0%, #26fcd8 100%);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;

      i {
        font-size: 20px;
        color: #fff;
      }
    }

    .card-title {
      font-size: 18px;
      font-weight: bold;
      color: #fff;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    }
  }

  .card-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .main-data {
    text-align: center;
    margin-bottom: 15px;

    .main-number {
      display: flex;
      align-items: baseline;
      justify-content: center;

      .main-unit {
        font-size: 24px;
        color: #26fcd8;
        font-weight: bold;
        margin-left: 8px;
      }
    }
  }

  .sub-data {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;

    .sub-item {
      flex: 1;
      text-align: center;

      &:not(:last-child) {
        border-right: 1px solid rgba(1, 153, 209, 0.3);
        margin-right: 10px;
        padding-right: 10px;
      }

      .sub-label {
        font-size: 12px;
        color: #a0a0a0;
        margin-bottom: 5px;
      }

      .sub-value {
        display: flex;
        align-items: baseline;
        justify-content: center;

        .sub-unit {
          font-size: 14px;
          color: #26fcd8;
          margin-left: 4px;
        }
      }
    }
  }

  .additional-info {
    margin-bottom: 10px;

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 0;
      font-size: 12px;

      .info-label {
        color: #a0a0a0;
      }

      .info-value {
        color: #26fcd8;
        font-weight: bold;
      }
    }
  }

  .progress-bar {
    position: relative;
    height: 6px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
    overflow: hidden;
    margin-top: auto;

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #03d3ec 0%, #26fcd8 100%);
      border-radius: 3px;
      transition: width 0.3s ease;
    }

    .progress-text {
      position: absolute;
      top: -20px;
      right: 0;
      font-size: 11px;
      color: #26fcd8;
      font-weight: bold;
    }
  }
}

// 响应式调整
@media (max-width: 1600px) {
  #digital-flop {
    .card-header .card-title {
      font-size: 16px;
    }

    .main-data .main-unit {
      font-size: 20px;
    }
  }
}
</style>
